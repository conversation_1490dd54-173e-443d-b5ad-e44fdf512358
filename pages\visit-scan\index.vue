<template>
  <view class="container">
    <!-- 扫码器容器 -->
    <view class="scan-box">
      <div
        id="visit-barcode-reader"
        class="barcode-reader"
        :style="{ width: '100%', height: 'auto' }"
      ></div>

      <!-- 扫描提示 -->
      <view class="tip-text" v-if="!isScanning">
        <view>将条形码放入框内进行扫描</view>
        <view class="barcode-tip">
          <span class="mode-restriction">走访专用条形码扫描</span>
        </view>
      </view>
    </view>

    <!-- 成功效果 -->
    <view v-if="showSuccessEffect" class="success-effect">
      <view class="success-icon">
        <uni-icons type="checkmarkempty" size="60" color="#fff"></uni-icons>
      </view>
      <view class="success-text">扫码成功</view>
    </view>

    <!-- 控制按钮 -->
    <view class="control-buttons">
      <view class="camera-toggle-btn" @click="toggleCamera" v-if="isScanning">
        <text>切换摄像头</text>
      </view>

      <view
        class="flashlight-btn"
        @click="toggleFlashlight"
        v-if="isScanning && supportsTorch"
      >
        <text>{{ flashlightOn ? "关闭闪光灯" : "开启闪光灯" }}</text>
      </view>

      <view class="manual-input-btn" @click="showManualInput">
        <text>手动输入</text>
      </view>

      <view class="scan-help-btn" @click="showScanTips">
        <text>扫码帮助</text>
      </view>
    </view>

    <!-- 手动输入弹窗 -->
    <uni-popup ref="inputPopup" type="center" :mask-click="false">
      <view class="input-popup">
        <view class="popup-header">
          <text class="popup-title">手动输入条形码</text>
        </view>
        <view class="popup-content">
          <input
            v-model="manualCode"
            class="manual-input"
            placeholder="请输入条形码"
            type="text"
            maxlength="50"
            @input="onManualInput"
          />
          <text class="input-tip"
            >请输入有效的条形码（数字或字母数字组合）</text
          >
        </view>
        <view class="popup-actions">
          <button class="cancel-btn" @click="closeManualInput">取消</button>
          <button
            class="confirm-btn"
            @click="confirmManualInput"
            :disabled="!isValidManualCode"
          >
            确认
          </button>
        </view>
      </view>
    </uni-popup>

    <!-- 扫码帮助弹窗 -->
    <uni-popup ref="tipsPopup" type="center" :mask-click="true">
      <view class="tips-popup">
        <view class="popup-header">
          <text class="popup-title">扫码使用帮助</text>
        </view>
        <view class="tips-content">
          <view class="tip-item">
            <text class="tip-title">• 条形码要求</text>
            <text class="tip-desc"
              >支持EAN-13、EAN-8、UPC-A、Code 128等常见条形码格式</text
            >
          </view>
          <view class="tip-item">
            <text class="tip-title">• 扫描技巧</text>
            <text class="tip-desc">保持条形码水平，距离适中，光线充足</text>
          </view>
          <view class="tip-item">
            <text class="tip-title">• 备选方案</text>
            <text class="tip-desc">如扫描失败，可使用"手动输入"功能</text>
          </view>
        </view>
        <view class="popup-actions">
          <button class="confirm-btn full-width" @click="closeTips">
            知道了
          </button>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import { Html5Qrcode, Html5QrcodeSupportedFormats } from "html5-qrcode";
import { getQrcode } from "@/api";

export default {
  data() {
    return {
      html5QrCode: null,
      isScanning: false,
      showSuccessEffect: false,
      lastScanTime: 0,
      isProcessing: false,
      isDestroyed: false,
      isInitializing: false,
      scanType: "1", // 走访类型
      flashlightOn: false,
      supportsTorch: false,
      currentCameraId: null,
      availableCameras: [],
      currentCameraIndex: 0,
      manualCode: "",
      isValidManualCode: false,
      // 条形码扫描配置
      barcodeConfig: {
        fps: 10,
        qrbox: { width: 350, height: 150 }, // 条形码通常是矩形
        aspectRatio: 1.0,
        disableFlip: false,
      },
    };
  },

  mounted() {
    console.log("[VisitScan] mounted");
    this.initializeScanner();
  },

  onShow() {
    console.log("[VisitScan] onShow");

    if (this.isDestroyed) {
      this.isDestroyed = false;
      // 延迟初始化，确保页面完全显示
      setTimeout(() => {
        this.initializeScanner();
      }, 500);
    } else if (!this.isScanning && this.html5QrCode) {
      // 延迟启动，确保页面完全显示
      setTimeout(() => {
        this.startScanning();
      }, 500);
    }
  },

  onHide() {
    console.log("[VisitScan] onHide");
    this.stopScanning();
  },

  onUnload() {
    console.log("[VisitScan] onUnload");
    this.isDestroyed = true;
    this.cleanup();
  },

  methods: {
    // 初始化扫码器
    async initializeScanner() {
      // 防止重复初始化
      if (this.isInitializing) {
        console.log("[VisitScan] 正在初始化中，跳过重复调用");
        return;
      }

      try {
        console.log("[VisitScan] 初始化扫码器");
        this.isInitializing = true;

        // 如果已经有实例，先清理
        if (this.html5QrCode) {
          await this.cleanup();
        }

        // 创建Html5Qrcode实例，配置支持条形码格式
        this.html5QrCode = new Html5Qrcode("visit-barcode-reader", {
          formatsToSupport: [
            Html5QrcodeSupportedFormats.EAN_13,
            Html5QrcodeSupportedFormats.EAN_8,
            Html5QrcodeSupportedFormats.UPC_A,
            Html5QrcodeSupportedFormats.UPC_E,
            Html5QrcodeSupportedFormats.CODE_128,
            Html5QrcodeSupportedFormats.CODE_39,
            Html5QrcodeSupportedFormats.CODE_93,
            Html5QrcodeSupportedFormats.ITF,
            Html5QrcodeSupportedFormats.CODABAR,
          ],
          verbose: false,
        });

        // 获取可用摄像头
        await this.getCameras();

        // 启动扫描
        await this.startScanning();
      } catch (error) {
        console.error("[VisitScan] 初始化扫码器失败:", error);
        uni.showToast({
          title: "扫码器初始化失败",
          icon: "none",
          duration: 2000,
        });
      } finally {
        this.isInitializing = false;
      }
    },

    // 获取可用摄像头
    async getCameras() {
      try {
        const cameras = await Html5Qrcode.getCameras();
        this.availableCameras = cameras || [];

        console.log("[VisitScan] 获取到摄像头列表:", this.availableCameras);

        // 多重策略优先选择后置摄像头
        let backCameraIndex = -1;

        // 策略1：明确包含"back"或"rear"或"environment"关键字的摄像头
        backCameraIndex = this.availableCameras.findIndex(
          (camera) =>
            camera.label &&
            (camera.label.toLowerCase().includes("back") ||
              camera.label.toLowerCase().includes("rear") ||
              camera.label.toLowerCase().includes("environment"))
        );

        // 策略2：Video device类型设备，选择数字最小的（通常是后置摄像头）
        if (backCameraIndex === -1) {
          const videoDevices = this.availableCameras.filter(
            (camera) =>
              camera.label &&
              camera.label.toLowerCase().includes("video device")
          );
          if (videoDevices.length > 0) {
            // 按设备ID排序，选择数字最小的
            videoDevices.sort((a, b) => {
              const aNum = parseInt(a.label.match(/\d+/)?.[0] || "999");
              const bNum = parseInt(b.label.match(/\d+/)?.[0] || "999");
              return aNum - bNum;
            });
            backCameraIndex = this.availableCameras.findIndex(
              (camera) => camera.id === videoDevices[0].id
            );
          }
        }

        // 策略3：选择第二个设备（通常第一个是前置，第二个是后置）
        if (backCameraIndex === -1 && this.availableCameras.length > 1) {
          backCameraIndex = 1;
        }

        // 策略4：兜底选择第一个设备
        if (backCameraIndex === -1 && this.availableCameras.length > 0) {
          backCameraIndex = 0;
        }

        this.currentCameraIndex = Math.max(0, backCameraIndex);
        this.currentCameraId =
          this.availableCameras[this.currentCameraIndex]?.id;

        console.log("[VisitScan] 选择摄像头:", {
          index: this.currentCameraIndex,
          camera: this.availableCameras[this.currentCameraIndex],
        });
      } catch (error) {
        console.error("[VisitScan] 获取摄像头失败:", error);
      }
    },

    // 开始扫描
    async startScanning() {
      if (this.isScanning || this.isDestroyed || !this.html5QrCode) {
        return;
      }

      try {
        console.log("[VisitScan] 开始扫描");
        this.isScanning = true;

        // 扫码成功回调
        const barcodeSuccessCallback = (decodedText, decodedResult) => {
          this.onScanSuccess(decodedText, decodedResult);
        };

        // 扫码失败回调（通常可以忽略）
        const barcodeErrorCallback = (errorMessage) => {
          // 静默处理扫描错误，避免控制台日志过多
        };

        // 尝试启动扫描，使用回退策略
        await this.tryStartScanning(
          barcodeSuccessCallback,
          barcodeErrorCallback
        );

        console.log("[VisitScan] 扫描已启动");

        // 检查是否支持闪光灯
        this.checkTorchSupport();
      } catch (error) {
        console.error("[VisitScan] 启动扫描失败:", error);
        this.isScanning = false;

        uni.showToast({
          title: "启动扫描失败，请检查摄像头权限",
          icon: "none",
          duration: 3000,
        });
      }
    },

    // 尝试启动扫描，使用回退策略
    async tryStartScanning(successCallback, errorCallback) {
      // 策略1：使用选定的摄像头设备ID
      if (this.currentCameraId) {
        try {
          console.log("[VisitScan] 尝试使用选定摄像头:", this.currentCameraId);
          await this.html5QrCode.start(
            { deviceId: { exact: this.currentCameraId } },
            this.barcodeConfig,
            successCallback,
            errorCallback
          );
          return; // 成功则返回
        } catch (error) {
          console.warn(
            "[VisitScan] 选定摄像头启动失败，尝试其他摄像头:",
            error
          );

          // 如果当前摄像头失败，尝试其他摄像头
          await this.tryOtherCameras(successCallback, errorCallback);
          return;
        }
      }

      // 策略2：使用facingMode约束
      try {
        console.log("[VisitScan] 使用facingMode约束启动");
        await this.html5QrCode.start(
          { facingMode: { ideal: "environment" } },
          this.barcodeConfig,
          successCallback,
          errorCallback
        );
      } catch (error) {
        console.warn("[VisitScan] facingMode启动失败:", error);
        throw error;
      }
    },

    // 尝试其他可用摄像头
    async tryOtherCameras(successCallback, errorCallback) {
      for (let i = 0; i < this.availableCameras.length; i++) {
        if (i === this.currentCameraIndex) {
          continue; // 跳过已经失败的摄像头
        }

        const camera = this.availableCameras[i];
        try {
          console.log(`[VisitScan] 尝试摄像头 ${i}:`, camera.label);
          await this.html5QrCode.start(
            { deviceId: { exact: camera.id } },
            this.barcodeConfig,
            successCallback,
            errorCallback
          );

          // 成功则更新当前摄像头
          this.currentCameraIndex = i;
          this.currentCameraId = camera.id;
          console.log("[VisitScan] 成功切换到摄像头:", camera.label);
          return;
        } catch (error) {
          console.warn(`[VisitScan] 摄像头 ${i} 启动失败:`, error);
          continue;
        }
      }

      // 所有摄像头都失败，抛出错误
      throw new Error("所有摄像头都无法启动");
    },

    // 停止扫描
    async stopScanning() {
      if (!this.isScanning || !this.html5QrCode) {
        return;
      }

      try {
        console.log("[VisitScan] 停止扫描");
        await this.html5QrCode.stop();
        this.isScanning = false;
        this.flashlightOn = false;
        this.supportsTorch = false;
      } catch (error) {
        console.error("[VisitScan] 停止扫描失败:", error);
      }
    },

    // 扫码成功处理
    async onScanSuccess(decodedText, decodedResult) {
      const currentTime = Date.now();

      // 防止重复扫描（500ms内）
      if (currentTime - this.lastScanTime < 500 || this.isProcessing) {
        return;
      }

      this.lastScanTime = currentTime;
      this.isProcessing = true;

      try {
        console.log("[VisitScan] 扫码成功:", decodedText);

        // 验证条形码格式
        if (!this.validateBarcodeResult(decodedText)) {
          uni.showToast({
            title: "请扫描有效的条形码",
            icon: "none",
            duration: 2000,
          });
          return;
        }

        // 显示成功效果
        this.showSuccessEffect = true;
        setTimeout(() => {
          this.showSuccessEffect = false;
        }, 1500);

        // 调用业务API验证条形码
        const response = await getQrcode({
          qrcode: decodedText,
          type: this.scanType,
        });

        if (response && response.code === 200) {
          console.log("[VisitScan] 条形码验证成功:", response.data);

          // 停止扫描
          await this.stopScanning();

          // 跳转到下一个页面，传递必要参数
          setTimeout(() => {
            uni.navigateTo({
              url: `/pages/visit-detail/index?barcode=${encodeURIComponent(
                decodedText
              )}&data=${encodeURIComponent(JSON.stringify(response.data))}`,
            });
          }, 1000);
        } else {
          throw new Error(response?.msg || "条形码验证失败");
        }
      } catch (error) {
        console.error("[VisitScan] 处理扫码结果失败:", error);

        uni.showToast({
          title: error.message || "条形码无效",
          icon: "none",
          duration: 2000,
        });
      } finally {
        // 延迟重置处理状态，避免快速重复扫描
        setTimeout(() => {
          this.isProcessing = false;
        }, 1000);
      }
    },

    // 验证条形码格式
    validateBarcodeResult(text) {
      if (!text || text.length < 1) {
        return false;
      }

      // 条形码通常是数字或数字字母组合
      // EAN-13: 13位数字
      if (/^\d{13}$/.test(text)) return true;
      // EAN-8: 8位数字
      if (/^\d{8}$/.test(text)) return true;
      // UPC-A: 12位数字
      if (/^\d{12}$/.test(text)) return true;
      // UPC-E: 6-8位数字
      if (/^\d{6,8}$/.test(text)) return true;
      // Code 128: 字母数字组合
      if (/^[A-Za-z0-9\-\s]+$/.test(text) && text.length >= 4) return true;
      // Code 39: 字母数字和特殊字符
      if (/^[A-Z0-9\-\.\s\$\/\+\%]+$/.test(text) && text.length >= 3)
        return true;
      // ITF: 偶数位数字
      if (/^\d+$/.test(text) && text.length % 2 === 0 && text.length >= 4)
        return true;

      return false;
    },

    // 切换摄像头
    async toggleCamera() {
      if (!this.isScanning || this.availableCameras.length <= 1) {
        return;
      }

      try {
        // 停止当前扫描
        await this.stopScanning();

        // 切换到下一个摄像头
        this.currentCameraIndex =
          (this.currentCameraIndex + 1) % this.availableCameras.length;
        this.currentCameraId =
          this.availableCameras[this.currentCameraIndex].id;

        console.log(
          "[VisitScan] 切换摄像头:",
          this.availableCameras[this.currentCameraIndex].label
        );

        // 重新启动扫描
        setTimeout(() => {
          this.startScanning();
        }, 500);
      } catch (error) {
        console.error("[VisitScan] 切换摄像头失败:", error);
      }
    },

    // 检查闪光灯支持
    checkTorchSupport() {
      try {
        // 检查是否为移动设备
        const isMobile =
          /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
            navigator.userAgent
          );

        if (!isMobile) {
          this.supportsTorch = false;
          return;
        }

        // 检查当前摄像头是否支持闪光灯
        if (this.html5QrCode) {
          try {
            const settings = this.html5QrCode.getRunningTrackSettings();
            this.supportsTorch = "torch" in settings;
            console.log("[VisitScan] 闪光灯支持检测:", this.supportsTorch);
          } catch (error) {
            console.log("[VisitScan] 无法检测闪光灯支持:", error);
            this.supportsTorch = isMobile; // 兜底策略
          }
        } else {
          this.supportsTorch = isMobile; // 兜底策略
        }
      } catch (error) {
        console.error("[VisitScan] 闪光灯支持检测失败:", error);
        this.supportsTorch = false;
      }
    },

    // 切换闪光灯
    async toggleFlashlight() {
      if (!this.supportsTorch || !this.html5QrCode) {
        console.log("[VisitScan] 闪光灯不支持或扫码器未初始化");
        return;
      }

      try {
        const newTorchState = !this.flashlightOn;

        // 检查是否支持闪光灯
        const runningTrackSettings = this.html5QrCode.getRunningTrackSettings();
        if (!("torch" in runningTrackSettings)) {
          console.log("[VisitScan] 当前设备不支持闪光灯");
          uni.showToast({
            title: "当前设备不支持闪光灯",
            icon: "none",
            duration: 2000,
          });
          return;
        }

        // 应用闪光灯约束
        const constraints = {
          torch: newTorchState,
          advanced: [{ torch: newTorchState }],
        };

        await this.html5QrCode.applyVideoConstraints(constraints);

        // 验证是否成功设置
        const updatedSettings = this.html5QrCode.getRunningTrackSettings();
        if (updatedSettings.torch === newTorchState) {
          this.flashlightOn = newTorchState;
          console.log(
            "[VisitScan] 闪光灯状态:",
            this.flashlightOn ? "开启" : "关闭"
          );

          uni.showToast({
            title: this.flashlightOn ? "闪光灯已开启" : "闪光灯已关闭",
            icon: "none",
            duration: 1000,
          });
        } else {
          throw new Error("闪光灯设置失败");
        }
      } catch (error) {
        console.error("[VisitScan] 切换闪光灯失败:", error);
        uni.showToast({
          title: "闪光灯操作失败",
          icon: "none",
          duration: 2000,
        });
      }
    },

    // 显示手动输入
    showManualInput() {
      this.manualCode = "";
      this.isValidManualCode = false;
      this.$refs.inputPopup.open();
    },

    // 关闭手动输入
    closeManualInput() {
      this.$refs.inputPopup.close();
      this.manualCode = "";
      this.isValidManualCode = false;
    },

    // 手动输入内容变化
    onManualInput(e) {
      const value = e.detail.value || e.target.value;
      this.manualCode = value;
      this.isValidManualCode = this.validateBarcodeResult(value);
    },

    // 确认手动输入
    async confirmManualInput() {
      if (!this.isValidManualCode) {
        return;
      }

      try {
        console.log("[VisitScan] 手动输入条形码:", this.manualCode);

        // 关闭弹窗
        this.closeManualInput();

        // 显示成功效果
        this.showSuccessEffect = true;
        setTimeout(() => {
          this.showSuccessEffect = false;
        }, 1500);

        // 调用业务API验证条形码
        const response = await getQrcode({
          qrcode: this.manualCode,
          type: this.scanType,
        });

        if (response && response.code === 200) {
          console.log("[VisitScan] 手动输入条形码验证成功:", response.data);

          // 停止扫描
          await this.stopScanning();

          // 跳转到下一个页面，传递必要参数
          setTimeout(() => {
            uni.navigateTo({
              url: `/pages/visit-detail/index?barcode=${encodeURIComponent(
                this.manualCode
              )}&data=${encodeURIComponent(JSON.stringify(response.data))}`,
            });
          }, 1000);
        } else {
          throw new Error(response?.msg || "条形码验证失败");
        }
      } catch (error) {
        console.error("[VisitScan] 手动输入处理失败:", error);

        uni.showToast({
          title: error.message || "条形码无效",
          icon: "none",
          duration: 2000,
        });
      }
    },

    // 显示扫码帮助
    showScanTips() {
      this.$refs.tipsPopup.open();
    },

    // 关闭扫码帮助
    closeTips() {
      this.$refs.tipsPopup.close();
    },

    // 清理资源
    async cleanup() {
      try {
        if (this.html5QrCode) {
          await this.stopScanning();
          this.html5QrCode = null;
        }

        this.isScanning = false;
        this.isProcessing = false;
        this.isInitializing = false;
        this.availableCameras = [];
        this.currentCameraId = null;

        console.log("[VisitScan] 资源清理完成");
      } catch (error) {
        console.error("[VisitScan] 清理资源失败:", error);
      }
    },
  },
};
</script>

<style scoped>
.container {
  position: relative;
  width: 100vw;
  height: 100vh;
  background: #000;
  overflow: hidden;
}

.scan-box {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.barcode-reader {
  width: 100%;
  height: auto;
  max-width: 100%;
}

/* 覆盖html5-qrcode的默认样式 */
:deep(#visit-barcode-reader) {
  border: none !important;
}

:deep(#visit-barcode-reader video) {
  width: 100% !important;
  height: auto !important;
  object-fit: cover;
}

:deep(#visit-barcode-reader canvas) {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
}

/* 自定义扫描框样式 - 条形码专用矩形框 */
:deep(#visit-barcode-reader) {
  position: relative;
}

:deep(#visit-barcode-reader)::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 350px;
  height: 150px;
  margin: -75px 0 0 -175px;
  border: 2px solid #ff6600;
  border-radius: 8px;
  box-shadow: 0 0 0 9999px rgba(0, 0, 0, 0.5);
  z-index: 1000;
  pointer-events: none;
}

.tip-text {
  position: absolute;
  bottom: 200px;
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
  color: #fff;
  z-index: 1001;
}

.tip-text > view:first-child {
  font-size: 16px;
  margin-bottom: 8px;
}

.barcode-tip {
  font-size: 14px;
}

.mode-restriction {
  background: rgba(255, 102, 0, 0.8);
  padding: 4px 12px;
  border-radius: 4px;
  color: #fff;
  font-weight: bold;
}

.success-effect {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #fff;
  z-index: 2000;
  background: rgba(0, 0, 0, 0.8);
  padding: 20px;
  border-radius: 12px;
}

.success-icon {
  margin-bottom: 10px;
}

.success-text {
  font-size: 16px;
  font-weight: bold;
}

.control-buttons {
  position: fixed;
  bottom: 30px;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-around;
  align-items: center;
  z-index: 1001;
  padding: 0 10px;
  gap: 8px;
}

.camera-toggle-btn,
.flashlight-btn,
.manual-input-btn,
.scan-help-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.7);
  color: #fff;
  padding: 10px 8px;
  border-radius: 8px;
  width: 70px;
  height: 60px;
  text-align: center;
  box-sizing: border-box;
}

.camera-toggle-btn text,
.flashlight-btn text,
.manual-input-btn text,
.scan-help-btn text {
  font-size: 11px;
  margin-top: 4px;
  line-height: 1.2;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 弹窗样式 */
.input-popup,
.tips-popup {
  background: #fff;
  border-radius: 12px;
  padding: 20px;
  max-width: 320px;
  width: 90vw;
}

.popup-header {
  text-align: center;
  margin-bottom: 20px;
}

.popup-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.popup-content {
  margin-bottom: 20px;
}

.manual-input {
  width: 100%;
  height: 40px;
  border: 1px solid #ddd;
  border-radius: 6px;
  padding: 0 12px;
  font-size: 16px;
  box-sizing: border-box;
}

.input-tip {
  display: block;
  margin-top: 8px;
  font-size: 12px;
  color: #999;
}

.popup-actions {
  display: flex;
  justify-content: space-between;
  gap: 12px;
}

.cancel-btn,
.confirm-btn {
  flex: 1;
  height: 40px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: bold;
}

.cancel-btn {
  background: #f5f5f5;
  color: #666;
}

.confirm-btn {
  background: #007aff;
  color: #fff;
}

.confirm-btn:disabled {
  background: #ccc;
  color: #999;
}

.confirm-btn.full-width {
  flex: none;
  width: 100%;
}

.tips-content {
  line-height: 1.6;
}

.tip-item {
  margin-bottom: 16px;
}

.tip-title {
  display: block;
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
}

.tip-desc {
  display: block;
  color: #666;
  font-size: 14px;
  padding-left: 16px;
}
</style>
