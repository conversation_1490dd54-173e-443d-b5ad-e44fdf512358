<template>
  <div class="container">
    <!-- 扫码器容器 -->
    <div class="scan-box">
      <div
        id="inspect-qr-reader"
        class="qrcode-reader"
        :style="{ width: '100%', height: 'auto' }"
      ></div>

      <!-- 扫描提示 -->
      <div class="tip-text" v-if="!isScanning">
        <div>将二维码放入框内进行扫描</div>
        <div class="qrcode-tip">
          <span class="mode-restriction">巡视专用二维码扫描</span>
        </div>
      </div>
    </div>

    <!-- 成功效果 -->
    <div v-if="showSuccessEffect" class="success-effect">
      <div class="success-icon">
        <uni-icons type="checkmarkempty" size="60" color="#fff"></uni-icons>
      </div>
      <div class="success-text">扫码成功</div>
    </div>

    <!-- 控制按钮 -->
    <div class="control-buttons">
      <view class="camera-toggle-btn" @click="toggleCamera" v-if="isScanning">
        <text>切换摄像头</text>
      </view>

      <view
        class="flashlight-btn"
        @click="toggleFlashlight"
        v-if="isScanning && supportsTorch"
      >
        <text>{{ flashlightOn ? "关闭闪光灯" : "开启闪光灯" }}</text>
      </view>
    </div>
  </div>
</template>

<script>
import { Html5Qrcode, Html5QrcodeSupportedFormats } from "html5-qrcode";
import { getQrcode } from "@/api";

export default {
  data() {
    return {
      html5QrCode: null,
      isScanning: false,
      showSuccessEffect: false,
      lastScanTime: 0,
      isProcessing: false,
      isDestroyed: false,
      isInitializing: false,
      scanType: "0", // 固定为巡视扫码类型
      flashlightOn: false,
      supportsTorch: false,
      currentCameraId: null,
      availableCameras: [],
      currentCameraIndex: 0,
      // 扫码配置
      qrCodeConfig: {
        fps: 10,
        qrbox: { width: 280, height: 280 },
        aspectRatio: 1.0,
        disableFlip: false,
      },
    };
  },

  mounted() {
    console.log("[InspectScan] mounted");
    this.initializeScanner();
  },

  onShow() {
    console.log("[InspectScan] onShow");

    if (this.isDestroyed) {
      this.isDestroyed = false;
      // 延迟初始化，确保页面完全显示
      setTimeout(() => {
        this.initializeScanner();
      }, 500);
    } else if (!this.isScanning && this.html5QrCode) {
      // 延迟启动，确保页面完全显示
      setTimeout(() => {
        this.startScanning();
      }, 500);
    }
  },

  onHide() {
    console.log("[InspectScan] onHide");
    this.stopScanning();
  },

  onUnload() {
    console.log("[InspectScan] onUnload");
    this.isDestroyed = true;
    this.cleanup();
  },

  methods: {
    // 初始化扫码器
    async initializeScanner() {
      // 防止重复初始化
      if (this.isInitializing) {
        console.log("[InspectScan] 正在初始化中，跳过重复调用");
        return;
      }

      try {
        console.log("[InspectScan] 初始化扫码器");
        this.isInitializing = true;

        // 如果已经有实例，先清理
        if (this.html5QrCode) {
          await this.cleanup();
        }

        // 创建Html5Qrcode实例
        this.html5QrCode = new Html5Qrcode("inspect-qr-reader", {
          formatsToSupport: [Html5QrcodeSupportedFormats.QR_CODE],
          verbose: false,
        });

        // 获取可用摄像头
        await this.getCameras();

        // 启动扫描
        await this.startScanning();
      } catch (error) {
        console.error("[InspectScan] 初始化扫码器失败:", error);
        uni.showToast({
          title: "扫码器初始化失败",
          icon: "none",
          duration: 2000,
        });
      } finally {
        this.isInitializing = false;
      }
    },

    // 获取可用摄像头
    async getCameras() {
      try {
        const cameras = await Html5Qrcode.getCameras();
        this.availableCameras = cameras || [];

        console.log("[InspectScan] 获取到摄像头列表:", this.availableCameras);

        // 多重策略优先选择后置摄像头
        let backCameraIndex = -1;

        // 策略1：明确包含"back"或"rear"或"environment"关键字的摄像头
        backCameraIndex = this.availableCameras.findIndex(
          (camera) =>
            camera.label &&
            (camera.label.toLowerCase().includes("back") ||
              camera.label.toLowerCase().includes("rear") ||
              camera.label.toLowerCase().includes("environment"))
        );

        // 策略2：Video device类型设备，选择数字最小的（通常是后置摄像头）
        if (backCameraIndex === -1) {
          const videoDevices = this.availableCameras.filter(
            (camera) =>
              camera.label &&
              camera.label.toLowerCase().includes("video device")
          );
          if (videoDevices.length > 0) {
            // 按设备ID排序，选择数字最小的
            videoDevices.sort((a, b) => {
              const aNum = parseInt(a.label.match(/\d+/)?.[0] || "999");
              const bNum = parseInt(b.label.match(/\d+/)?.[0] || "999");
              return aNum - bNum;
            });
            backCameraIndex = this.availableCameras.findIndex(
              (camera) => camera.id === videoDevices[0].id
            );
          }
        }

        // 策略3：选择第二个设备（通常第一个是前置，第二个是后置）
        if (backCameraIndex === -1 && this.availableCameras.length > 1) {
          backCameraIndex = 1;
        }

        // 策略4：兜底选择第一个设备
        if (backCameraIndex === -1 && this.availableCameras.length > 0) {
          backCameraIndex = 0;
        }

        this.currentCameraIndex = Math.max(0, backCameraIndex);
        this.currentCameraId =
          this.availableCameras[this.currentCameraIndex]?.id;

        console.log("[InspectScan] 选择摄像头:", {
          index: this.currentCameraIndex,
          camera: this.availableCameras[this.currentCameraIndex],
        });
      } catch (error) {
        console.error("[InspectScan] 获取摄像头失败:", error);
      }
    },

    // 开始扫描
    async startScanning() {
      if (this.isScanning || this.isDestroyed || !this.html5QrCode) {
        return;
      }

      try {
        console.log("[InspectScan] 开始扫描");
        this.isScanning = true;

        // 扫码成功回调
        const qrCodeSuccessCallback = (decodedText, decodedResult) => {
          this.onScanSuccess(decodedText, decodedResult);
        };

        // 扫码失败回调（通常可以忽略）
        const qrCodeErrorCallback = (errorMessage) => {
          // 静默处理扫描错误，避免控制台日志过多
          // console.warn("[InspectScan] 扫描错误:", errorMessage);
        };

        // 尝试启动扫描，使用回退策略
        await this.tryStartScanning(qrCodeSuccessCallback, qrCodeErrorCallback);

        console.log("[InspectScan] 扫描已启动");

        // 检查是否支持闪光灯
        this.checkTorchSupport();
      } catch (error) {
        console.error("[InspectScan] 启动扫描失败:", error);
        this.isScanning = false;

        uni.showToast({
          title: "启动扫描失败，请检查摄像头权限",
          icon: "none",
          duration: 3000,
        });
      }
    },

    // 尝试启动扫描，使用回退策略
    async tryStartScanning(successCallback, errorCallback) {
      // 策略1：使用选定的摄像头设备ID
      if (this.currentCameraId) {
        try {
          console.log(
            "[InspectScan] 尝试使用选定摄像头:",
            this.currentCameraId
          );
          await this.html5QrCode.start(
            { deviceId: { exact: this.currentCameraId } },
            this.qrCodeConfig,
            successCallback,
            errorCallback
          );
          return; // 成功则返回
        } catch (error) {
          console.warn(
            "[InspectScan] 选定摄像头启动失败，尝试其他摄像头:",
            error
          );

          // 如果当前摄像头失败，尝试其他摄像头
          await this.tryOtherCameras(successCallback, errorCallback);
          return;
        }
      }

      // 策略2：使用facingMode约束
      try {
        console.log("[InspectScan] 使用facingMode约束启动");
        await this.html5QrCode.start(
          { facingMode: { ideal: "environment" } },
          this.qrCodeConfig,
          successCallback,
          errorCallback
        );
      } catch (error) {
        console.warn("[InspectScan] facingMode启动失败:", error);
        throw error;
      }
    },

    // 尝试其他可用摄像头
    async tryOtherCameras(successCallback, errorCallback) {
      for (let i = 0; i < this.availableCameras.length; i++) {
        if (i === this.currentCameraIndex) {
          continue; // 跳过已经失败的摄像头
        }

        const camera = this.availableCameras[i];
        try {
          console.log(`[InspectScan] 尝试摄像头 ${i}:`, camera.label);
          await this.html5QrCode.start(
            { deviceId: { exact: camera.id } },
            this.qrCodeConfig,
            successCallback,
            errorCallback
          );

          // 成功则更新当前摄像头
          this.currentCameraIndex = i;
          this.currentCameraId = camera.id;
          console.log("[InspectScan] 成功切换到摄像头:", camera.label);
          return;
        } catch (error) {
          console.warn(`[InspectScan] 摄像头 ${i} 启动失败:`, error);
          continue;
        }
      }

      // 所有摄像头都失败，抛出错误
      throw new Error("所有摄像头都无法启动");
    },

    // 停止扫描
    async stopScanning() {
      if (!this.isScanning || !this.html5QrCode) {
        return;
      }

      try {
        console.log("[InspectScan] 停止扫描");
        await this.html5QrCode.stop();
        this.isScanning = false;
        this.flashlightOn = false;
        this.supportsTorch = false;
      } catch (error) {
        console.error("[InspectScan] 停止扫描失败:", error);
      }
    },

    // 扫码成功处理
    async onScanSuccess(decodedText, decodedResult) {
      const currentTime = Date.now();

      // 防止重复扫描（500ms内）
      if (currentTime - this.lastScanTime < 500 || this.isProcessing) {
        return;
      }

      this.lastScanTime = currentTime;
      this.isProcessing = true;

      try {
        console.log("[InspectScan] 扫码成功:", decodedText);

        // 显示成功效果
        this.showSuccessEffect = true;
        setTimeout(() => {
          this.showSuccessEffect = false;
        }, 1500);

        // 调用业务API验证二维码
        const response = await getQrcode({
          qrcode: decodedText,
          type: this.scanType,
        });

        if (response && response.code === 200) {
          console.log("[InspectScan] 二维码验证成功:", response.data);

          // 停止扫描
          await this.stopScanning();

          // 跳转到下一个页面，传递必要参数
          setTimeout(() => {
            uni.navigateTo({
              url: `/pages/inspect-detail/index?qrcode=${encodeURIComponent(
                decodedText
              )}&data=${encodeURIComponent(JSON.stringify(response.data))}`,
            });
          }, 1000);
        } else {
          throw new Error(response?.msg || "二维码验证失败");
        }
      } catch (error) {
        console.error("[InspectScan] 处理扫码结果失败:", error);

        uni.showToast({
          title: error.message || "二维码无效",
          icon: "none",
          duration: 2000,
        });
      } finally {
        // 延迟重置处理状态，避免快速重复扫描
        setTimeout(() => {
          this.isProcessing = false;
        }, 1000);
      }
    },

    // 切换摄像头
    async toggleCamera() {
      if (!this.isScanning || this.availableCameras.length <= 1) {
        return;
      }

      try {
        // 停止当前扫描
        await this.stopScanning();

        // 切换到下一个摄像头
        this.currentCameraIndex =
          (this.currentCameraIndex + 1) % this.availableCameras.length;
        this.currentCameraId =
          this.availableCameras[this.currentCameraIndex].id;

        console.log(
          "[InspectScan] 切换摄像头:",
          this.availableCameras[this.currentCameraIndex].label
        );

        // 重新启动扫描
        setTimeout(() => {
          this.startScanning();
        }, 500);
      } catch (error) {
        console.error("[InspectScan] 切换摄像头失败:", error);
      }
    },

    // 检查闪光灯支持
    checkTorchSupport() {
      try {
        // 检查是否为移动设备
        const isMobile =
          /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
            navigator.userAgent
          );

        if (!isMobile) {
          this.supportsTorch = false;
          return;
        }

        // 检查当前摄像头是否支持闪光灯
        if (this.html5QrCode) {
          try {
            const settings = this.html5QrCode.getRunningTrackSettings();
            this.supportsTorch = "torch" in settings;
            console.log("[InspectScan] 闪光灯支持检测:", this.supportsTorch);
          } catch (error) {
            console.log("[InspectScan] 无法检测闪光灯支持:", error);
            this.supportsTorch = isMobile; // 兜底策略
          }
        } else {
          this.supportsTorch = isMobile; // 兜底策略
        }
      } catch (error) {
        console.error("[InspectScan] 闪光灯支持检测失败:", error);
        this.supportsTorch = false;
      }
    },

    // 切换闪光灯
    async toggleFlashlight() {
      if (!this.supportsTorch || !this.html5QrCode) {
        console.log("[InspectScan] 闪光灯不支持或扫码器未初始化");
        return;
      }

      try {
        const newTorchState = !this.flashlightOn;

        // 检查是否支持闪光灯
        const runningTrackSettings = this.html5QrCode.getRunningTrackSettings();
        if (!("torch" in runningTrackSettings)) {
          console.log("[InspectScan] 当前设备不支持闪光灯");
          uni.showToast({
            title: "当前设备不支持闪光灯",
            icon: "none",
            duration: 2000,
          });
          return;
        }

        // 应用闪光灯约束
        const constraints = {
          torch: newTorchState,
          advanced: [{ torch: newTorchState }],
        };

        await this.html5QrCode.applyVideoConstraints(constraints);

        // 验证是否成功设置
        const updatedSettings = this.html5QrCode.getRunningTrackSettings();
        if (updatedSettings.torch === newTorchState) {
          this.flashlightOn = newTorchState;
          console.log(
            "[InspectScan] 闪光灯状态:",
            this.flashlightOn ? "开启" : "关闭"
          );

          uni.showToast({
            title: this.flashlightOn ? "闪光灯已开启" : "闪光灯已关闭",
            icon: "none",
            duration: 1000,
          });
        } else {
          throw new Error("闪光灯设置失败");
        }
      } catch (error) {
        console.error("[InspectScan] 切换闪光灯失败:", error);
        uni.showToast({
          title: "闪光灯操作失败",
          icon: "none",
          duration: 2000,
        });
      }
    },

    // 清理资源
    async cleanup() {
      try {
        if (this.html5QrCode) {
          await this.stopScanning();
          this.html5QrCode = null;
        }

        this.isScanning = false;
        this.isProcessing = false;
        this.isInitializing = false;
        this.availableCameras = [];
        this.currentCameraId = null;

        console.log("[InspectScan] 资源清理完成");
      } catch (error) {
        console.error("[InspectScan] 清理资源失败:", error);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  position: relative;
  width: 100vw;
  height: 100vh;
  background-color: #000;
  overflow: hidden;
}

.scan-box {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.qrcode-reader {
  width: 100%;
  height: auto;
  max-width: 100%;
}

/* 覆盖html5-qrcode的默认样式 */
:deep(#inspect-qr-reader) {
  border: none !important;
}

:deep(#inspect-qr-reader video) {
  width: 100% !important;
  height: auto !important;
  object-fit: cover;
}

:deep(#inspect-qr-reader canvas) {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
}

/* 自定义扫描框样式 */
:deep(#inspect-qr-reader) {
  position: relative;
}

:deep(#inspect-qr-reader)::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 280px;
  height: 280px;
  margin: -140px 0 0 -140px;
  border: 2px solid #00ff00;
  border-radius: 8px;
  box-shadow: 0 0 0 9999px rgba(0, 0, 0, 0.5);
  z-index: 1000;
  pointer-events: none;
}

.tip-text {
  position: absolute;
  bottom: 120px;
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
  color: #fff;
  z-index: 1001;
}

.tip-text > div:first-child {
  font-size: 16px;
  margin-bottom: 8px;
}

.qrcode-tip {
  font-size: 14px;
}

.mode-restriction {
  background: rgba(0, 255, 0, 0.8);
  padding: 4px 12px;
  border-radius: 4px;
  color: #000;
  font-weight: bold;
}

.success-effect {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #fff;
  z-index: 2000;
  background: rgba(0, 0, 0, 0.8);
  padding: 20px;
  border-radius: 12px;
}

.success-icon {
  margin-bottom: 10px;
}

.success-text {
  font-size: 16px;
  font-weight: bold;
}

.control-buttons {
  position: fixed;
  bottom: 30px;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-around;
  align-items: center;
  z-index: 1001;
  padding: 0 20px;
  gap: 12px;
}

.camera-toggle-btn,
.flashlight-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.7);
  color: #fff;
  padding: 10px 8px;
  border-radius: 8px;
  width: 70px;
  height: 60px;
  text-align: center;
  box-sizing: border-box;
}

.camera-toggle-btn text,
.flashlight-btn text {
  font-size: 11px;
  margin-top: 4px;
  line-height: 1.2;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
