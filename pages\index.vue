<template>
  <div class="index-container">
    <van-pull-refresh
      v-model="isRefreshing"
      @refresh="onRefresh"
      :head-height="80"
    >
      <div class="w-full box-border">
        <!-- 顶部主Tab -->
        <van-tabs v-model:active="mainTab" sticky>
          <!-- 走访Tab -->
          <van-tab title="走访" name="visit">
            <!-- 子Tab -->
            <van-tabs v-model:active="visitSubTab">
              <van-tab title="任务列表" name="task">
                <!-- 搜索框 -->
                <van-search
                  v-model="searchValue"
                  :placeholder="
                    mainTab === 'inspect' ? '请输入台区名称' : '请输入客户名称'
                  "
                  @search="onSearch"
                />
                <!-- 任务列表内容 -->
                <van-list
                  v-model:loading="loadingMore"
                  :finished="finished"
                  finished-text="没有更多了"
                  @load="onLoadMore"
                  :immediate-check="false"
                >
                  <template v-if="loading">
                    <div class="flex flex-col gap-4 mt-4">
                      <van-skeleton
                        :row="4"
                        :loading="loading"
                        v-for="i in 3"
                        :key="i"
                      >
                        <template #template>
                          <div class="skeleton-card">
                            <div class="skeleton-header"></div>
                            <div class="skeleton-content">
                              <div
                                class="skeleton-row"
                                v-for="j in 4"
                                :key="j"
                              ></div>
                            </div>
                            <div class="skeleton-footer">
                              <div class="skeleton-button"></div>
                              <div class="skeleton-button"></div>
                              <div class="skeleton-button"></div>
                            </div>
                          </div>
                        </template>
                      </van-skeleton>
                    </div>
                  </template>

                  <template
                    v-else-if="
                      !loading && (!currentOrders || currentOrders.length === 0)
                    "
                  >
                    <div
                      class="flex flex-col justify-center items-center mt-32"
                    >
                      <van-empty
                        class="custom-empty"
                        image="search"
                        description="暂无数据"
                      >
                      </van-empty>
                      <van-button
                        type="primary"
                        block
                        class="mt-4"
                        @click="onRefresh"
                      >
                        刷新
                      </van-button>
                    </div>
                  </template>

                  <!-- 工单列表区域 -->
                  <div v-else class="flex flex-col gap-4 mt-4">
                    <div
                      v-for="(item, index) in currentOrders"
                      :key="index"
                      class="shadow-md w-full h-auto rounded p-6 box-border flex flex-col gap-2"
                    >
                      <div class="flex justify-end w-full font-bold text-type">
                        {{ getFormTypeLabel(item.formType) }}
                      </div>
                      <!-- 使用 fields 配置数组循环渲染字段 -->
                      <div
                        v-for="(field, idx) in item.fields"
                        :key="idx"
                        class="flex items-center"
                      >
                        <div class="font-bold text-black text-lg mr-2">
                          {{ field.label }}
                        </div>
                        <div class="text-gray-500 text-md">
                          <template v-if="field.type === 'tags'">
                            <van-space>
                              <van-tag
                                type="primary"
                                v-for="tag in item[field.key]"
                                :key="tag.tagId"
                              >
                                {{ tag.tagName }}
                              </van-tag>
                            </van-space>
                          </template>
                          <template v-else-if="field.key === 'formStatus'">
                            {{ getStatusText(item[field.key]) }}
                          </template>
                          <template v-else>
                            {{
                              field.key === "datasource"
                                ? getDatasource(item[field.key])
                                : item[field.key]
                            }}
                          </template>
                        </div>
                      </div>
                      <!-- 按钮组 - 在自主填报的按钮组部分 -->
                      <div class="flex justify-between items-center gap-4">
                        <van-button
                          v-if="
                            shouldShowButton(
                              item.formType,
                              'navigation',
                              activeTab
                            )
                          "
                          icon="map-marked"
                          type="info"
                          :class="getButtonWidthClass(item.formType, activeTab)"
                          block
                          @click="onGetActionSheet(item, 'navigation')"
                          >导航</van-button
                        >
                        <van-button
                          v-if="
                            shouldShowButton(item.formType, 'phone', activeTab)
                          "
                          icon="phone"
                          type="primary"
                          :class="getButtonWidthClass(item.formType, activeTab)"
                          block
                          @click="onGetActionSheet(item, 'phone')"
                          >电话</van-button
                        >
                        <van-button
                          v-if="
                            shouldShowButton(item.formType, 'submit', activeTab)
                          "
                          icon="edit"
                          type="primary"
                          :class="getButtonWidthClass(item.formType, activeTab)"
                          block
                          @click="onSubmitForm(item)"
                          >填报</van-button
                        >
                        <van-button
                          icon="info"
                          type="danger"
                          :class="getButtonWidthClass(item.formType, activeTab)"
                          block
                          @click="onGetActionSheet(item, 'exception')"
                          >异常</van-button
                        >
                      </div>
                    </div>
                  </div>
                </van-list>

                <!-- 走访扫一扫按钮 -->
                <div class="fixed-scan-btn" @click="goToVisitScan">
                  <div class="scan-btn-inner">
                    <img :src="qrcodeicon" alt="qrcodeicon" class="w-6 mr-2" />
                    <span>扫一扫</span>
                  </div>
                </div>
              </van-tab>
              <van-tab title="手动填报" name="manual">
                <div class="flex justify-center items-center h-[70vh]">
                  <van-button
                    type="primary"
                    size="large"
                    block
                    @click="goToManualForm"
                  >
                    开始填报
                  </van-button>
                </div>
              </van-tab>
            </van-tabs>
          </van-tab>

          <!-- 巡视Tab -->
          <van-tab title="巡视" name="inspect">
            <!-- 巡视子Tab -->
            <van-tabs v-model:active="inspectSubTab">
              <van-tab title="任务列表" name="task">
                <div class="w-full box-border p-4">
                  <div class="relative w-full" @click="goToScan">
                    <img :src="qrcode" alt="qrcode" class="w-full" />
                    <div
                      class="absolute left-4 top-1/2 transform -translate-y-1/2 text-white text-xl"
                    >
                      扫一扫
                    </div>
                    <img
                      :src="qrcodeicon"
                      alt="qrcodeicon"
                      class="absolute w-6 right-4 top-1/2 transform -translate-y-1/2"
                    />
                  </div>
                </div>

                <!-- 巡视列表部分 -->
                <div class="w-full box-border">
                  <!-- 搜索框 -->
                  <van-search
                    v-model="inspectSearchValue"
                    placeholder="请输入台区名称"
                    @search="onInspectSearch"
                  />

                  <!-- 巡视列表 -->
                  <van-list
                    v-model:loading="inspectLoadingMore"
                    :finished="inspectFinished"
                    finished-text="没有更多了"
                    @load="onInspectLoadMore"
                    :immediate-check="false"
                  >
                    <template v-if="inspectLoading">
                      <div class="flex flex-col gap-4 mt-4">
                        <van-skeleton
                          :row="4"
                          :loading="inspectLoading"
                          v-for="i in 3"
                          :key="i"
                        >
                          <template #template>
                            <div class="skeleton-card">
                              <div class="skeleton-header"></div>
                              <div class="skeleton-content">
                                <div
                                  class="skeleton-row"
                                  v-for="j in 4"
                                  :key="j"
                                ></div>
                              </div>
                              <div class="skeleton-footer">
                                <div class="skeleton-button"></div>
                                <div class="skeleton-button"></div>
                                <div class="skeleton-button"></div>
                              </div>
                            </div>
                          </template>
                        </van-skeleton>
                      </div>
                    </template>

                    <template
                      v-else-if="
                        !inspectLoading &&
                        (!inspectOrders || inspectOrders.length === 0)
                      "
                    >
                      <div
                        class="flex flex-col justify-center items-center mt-32"
                      >
                        <van-empty
                          class="custom-empty"
                          image="search"
                          description="暂无数据"
                        >
                        </van-empty>
                        <van-button
                          type="primary"
                          block
                          class="mt-4"
                          @click="onInspectRefresh"
                        >
                          刷新
                        </van-button>
                      </div>
                    </template>

                    <!-- 巡视工单列表区域 -->
                    <div v-else class="flex flex-col gap-4 mt-4">
                      <div
                        v-for="(item, index) in inspectOrders"
                        :key="index"
                        class="shadow-md w-full h-auto rounded p-6 box-border flex flex-col gap-2"
                      >
                        <div
                          class="flex justify-end w-full font-bold text-type"
                        >
                          {{ getFormTypeLabel(item.formType) }}
                        </div>
                        <!-- 使用 fields 配置数组循环渲染字段 -->
                        <div
                          v-for="(field, idx) in item.fields"
                          :key="idx"
                          class="flex items-center"
                        >
                          <div class="font-bold text-black text-lg mr-2">
                            {{ field.label }}
                          </div>
                          <div class="text-gray-500 text-md">
                            <template v-if="field.type === 'tags'">
                              <van-space>
                                <van-tag
                                  type="primary"
                                  v-for="tag in item[field.key]"
                                  :key="tag.tagId"
                                >
                                  {{ tag.tagName }}
                                </van-tag>
                              </van-space>
                            </template>
                            <template v-else-if="field.key === 'formStatus'">
                              {{ getStatusText(item[field.key]) }}
                            </template>
                            <template v-else-if="field.key === 'createTime'">
                              {{ formDataTimeFormat(item[field.key]) }}
                            </template>
                            <template v-else>
                              {{
                                field.key === "datasource"
                                  ? getDatasource(item[field.key])
                                  : item[field.key]
                              }}
                            </template>
                          </div>
                        </div>
                        <!-- 按钮组 - 巡视工单的按钮组 -->
                        <div class="flex justify-between items-center gap-4">
                          <van-button
                            v-if="
                              shouldShowButton(
                                item.formType,
                                'navigation',
                                'inspect'
                              )
                            "
                            icon="map-marked"
                            type="info"
                            :class="
                              getButtonWidthClass(item.formType, 'inspect')
                            "
                            block
                            @click="onGetActionSheet(item, 'navigation')"
                            >导航</van-button
                          >
                          <van-button
                            v-if="
                              shouldShowButton(
                                item.formType,
                                'phone',
                                'inspect'
                              )
                            "
                            icon="phone"
                            type="primary"
                            :class="
                              getButtonWidthClass(item.formType, 'inspect')
                            "
                            block
                            @click="onGetActionSheet(item, 'phone')"
                            >电话</van-button
                          >
                          <van-button
                            icon="edit"
                            type="primary"
                            :class="
                              getButtonWidthClass(item.formType, 'inspect')
                            "
                            block
                            @click="onInspectFormSubmit(item)"
                            >填报</van-button
                          >
                        </div>
                      </div>
                    </div>
                  </van-list>
                </div>
              </van-tab>

              <van-tab title="手动填报" name="manual">
                <div class="flex justify-center items-center h-[70vh]">
                  <van-button
                    type="primary"
                    size="large"
                    block
                    @click="goToInspectManualForm"
                  >
                    开始填报
                  </van-button>
                </div>
              </van-tab>
            </van-tabs>
          </van-tab>

          <!-- 数据看板Tab -->
          <van-tab title="数据看板" name="board">
            <board-component v-if="mainTab === 'board'" />
          </van-tab>
        </van-tabs>

        <!-- ActionSheet保持不变 -->
        <van-action-sheet
          v-model:show="show"
          :actions="actions"
          @select="onSelect"
          cancel-text="取消"
          close-on-click-action
          @cancel="onCancel"
        />
      </div>
    </van-pull-refresh>
  </div>
</template>

<script>
import qrcode from "@/static/images/qrcode-bg.png";
import board from "@/static/images/dashboard-bg.png";
import qrcodeicon from "@/static/images/qrcode-icon.png";
import boardicon from "@/static/images/dashboard-icon.png";
import {
  List,
  PullRefresh,
  ActionSheet,
  Skeleton,
  Empty,
  Tab,
  Tabs,
  Search,
  Space,
  Tag,
} from "vant";
import {
  getFormListByPage,
  getFormListInformation,
  getDefaultFormByNoOrder,
} from "@/api/index";
import { isProd } from "@/utils/env";
import { writeLog, LogLevel } from "@/utils/logger";

export default {
  components: {
    "van-list": List,
    "van-pull-refresh": PullRefresh,
    "van-action-sheet": ActionSheet,
    "van-skeleton": Skeleton,
    "van-empty": Empty,
    "van-tab": Tab,
    "van-tabs": Tabs,
    "van-search": Search,
    "van-space": Space,
    "van-tag": Tag,
    "board-component": () => import("@/pages/board/index.vue"),
  },
  data() {
    return {
      // 顶部卡片所需图片资源
      qrcode,
      board,
      qrcodeicon,
      board,
      boardicon,
      // 接口返回的工单列表
      orders: [],
      isRefreshing: false,
      show: false,
      actions: [],
      formInfo: null, // 添加表单详情数据
      loading: false, // 修改默认值为 false
      // 添加agentId配置
      agentId: navigator.userAgent.toLowerCase().includes("uat")
        ? "1006802"
        : "1001657",
      activeTab: "no-form", // 修改默认选中的标签页
      taskOrders: [], // 工单任务数据
      selfOrders: [], // 自主填报数据
      currentOrders: [], // 添加当前显示的工单列表
      initialHistoryState: null, // 添加初始历史状态存储
      // 添加分页相关数据
      pageNum: 1,
      pageSize: 10,
      loadingMore: false,
      finished: false,
      mainTab: "visit",
      visitSubTab: "task",
      searchValue: "",
      // 添加一个已初始化标志
      hasInitialized: false,
      // 巡视相关数据
      inspectOrders: [], // 巡视工单数据
      allInspectOrders: [], // 所有巡视工单数据（用于前端分页）
      inspectLoading: false, // 巡视加载状态
      inspectLoadingMore: false, // 巡视加载更多状态
      inspectFinished: false, // 巡视是否加载完成
      inspectSearchValue: "", // 巡视搜索值
      inspectPageNum: 1, // 巡视页码
      inspectPageSize: 10, // 巡视每页大小
      inspectSubTab: "task", // 添加巡视子Tab状态
    };
  },
  watch: {
    activeTab: {
      handler(newTab) {
        this.currentOrders =
          newTab === "task" ? this.taskOrders : this.selfOrders;

        // 只有在已初始化且tab改变时才重新获取数据
        if (this.hasInitialized) {
          this.fetchOrders();
        }
      },
    },
    taskOrders: {
      handler(newVal) {
        if (this.activeTab === "task") {
          this.currentOrders = newVal;
        }
      },
    },
    selfOrders: {
      handler(newVal) {
        if (this.visitSubTab === "task") {
          this.currentOrders = newVal;
        }
      },
    },
    mainTab: {
      handler(newVal) {
        if (newVal === "board") {
          // 清除其他tab的数据状态
          this.currentOrders = [];
          this.loading = false;
          this.inspectOrders = [];
          this.allInspectOrders = [];
          this.inspectLoading = false;
        } else if (newVal === "visit" && this.visitSubTab === "task") {
          // 只有在已初始化后的tab变化才触发请求
          if (this.hasInitialized) {
            this.fetchOrders();
          }
        } else if (newVal === "inspect" && this.inspectSubTab === "task") {
          // 切换到巡视tab时获取数据
          if (this.hasInitialized) {
            this.fetchInspectOrders();
          }
        }
      },
    },
    // 添加巡视子Tab的监听
    inspectSubTab: {
      handler(newVal) {
        if (this.mainTab === "inspect" && newVal === "task") {
          // 只有在已初始化后的tab变化才触发请求
          if (this.hasInitialized) {
            this.fetchInspectOrders();
          }
        }
      },
    },
  },
  onPullDownRefresh() {
    this.fetchOrders().then(() => {
      uni.stopPullDownRefresh();
    });
  },
  onShow() {
    // 记录初始历史状态
    this.initialHistoryState = JSON.stringify(window.history.state);

    // 监听安卓物理返回按钮
    window.handleClientGoBack = () => {
      this.onNavigationBarButtonTap();
    };

    // 只在第一次显示页面时获取数据
    if (!this.hasInitialized) {
      // 根据当前tab获取对应数据
      if (this.mainTab === "visit" && this.visitSubTab === "task") {
        this.fetchOrders();
      } else if (this.mainTab === "inspect" && this.inspectSubTab === "task") {
        this.fetchInspectOrders();
      }
      this.hasInitialized = true;
    } else {
      // 添加判断：如果是走访-任务列表页，也需要刷新数据
      // 这样可以保证从其他页面返回时数据是最新的
      if (this.mainTab === "visit" && this.visitSubTab === "task") {
        this.fetchOrders();
      } else if (this.mainTab === "inspect" && this.inspectSubTab === "task") {
        this.fetchInspectOrders();
      }
    }
  },
  methods: {
    // 修改 fetchOrders 方法
    async fetchOrders(isLoadMore = false) {
      // 添加DEBUG日志，跟踪调用来源
      console.log("fetchOrders被调用", {
        isLoadMore,
        mainTab: this.mainTab,
        visitSubTab: this.visitSubTab,
        caller: new Error().stack,
      });

      // 记录一个特殊的日志以跟踪调用栈
      writeLog(LogLevel.INFO, "index页面", "fetchOrders被调用", {
        isLoadMore,
        mainTab: this.mainTab,
        visitSubTab: this.visitSubTab,
        hasInitialized: this.hasInitialized,
      });

      // 只在走访-任务列表tab下获取数据
      if (this.mainTab !== "visit" || this.visitSubTab !== "task") {
        this.loading = false; // 添加这行，确保其他tab下不显示loading
        return;
      }

      if (!isLoadMore) {
        this.loading = true;
        this.pageNum = 1;
        this.finished = false;
        this.selfOrders = [];
      }

      try {
        const params = {
          pageNum: this.pageNum,
          pageSize: this.pageSize,
        };

        // 添加搜索条件
        if (this.searchValue) {
          if (this.mainTab === "inspect") {
            params.towerName = this.searchValue;
          } else {
            params.customName = this.searchValue; // 修改这里:从 userName 改为 customName
          }
        }

        // 记录API调用前的参数信息
        writeLog(LogLevel.INFO, "index页面", "调用getFormListByPage前", {
          params,
          isLoadMore,
          currentTab: this.mainTab,
          subTab: this.visitSubTab,
        });

        // 记录API开始时间
        const startTime = new Date().getTime();
        const response = await getFormListByPage(params);
        // 计算API响应时间
        const apiTime = new Date().getTime() - startTime;

        // 记录完整的API响应
        writeLog(LogLevel.INFO, "index页面", "getFormListByPage返回结果", {
          resData: JSON.stringify(response),
          code: response?.code,
          msg: response?.msg,
          total: response?.total,
          rowsCount: response?.rows?.length,
          apiTime: `${apiTime}ms`,
          firstItem: response?.rows?.[0]
            ? JSON.stringify(response?.rows?.[0])
            : null,
        });

        const { total = 0, rows = [] } = response;

        if (isLoadMore) {
          this.selfOrders = [...this.selfOrders, ...rows];
        } else {
          this.selfOrders = rows;
        }

        // 记录数据处理结果
        writeLog(LogLevel.INFO, "index页面", "处理getFormListByPage数据", {
          totalRecords: total,
          receivedRows: rows.length,
          currentDisplayCount: this.selfOrders.length,
          isLoadingFinished:
            this.selfOrders.length >= total || rows.length === 0,
        });

        // 更新字段显示
        if (rows.length > 0) {
          this.updateFields(this.selfOrders);
        }

        // 更新加载状态
        this.finished = this.selfOrders.length >= total || rows.length === 0;
      } catch (error) {
        // 详细记录错误信息
        writeLog(LogLevel.ERROR, "index页面", "getFormListByPage调用失败", {
          error: error.message,
          stack: error.stack,
          params: {
            pageNum: this.pageNum,
            pageSize: this.pageSize,
            searchValue: this.searchValue,
          },
        });
        console.error("获取工单列表失败:", error);

        // 优先使用服务器返回的msg字段
        let errorMsg = "获取工单列表失败";
        if (error.response?.msg) {
          errorMsg = error.response.msg;
        } else if (error.message) {
          errorMsg = error.message;
        }
        uni.showToast({
          title: errorMsg,
          icon: "none",
        });

        this.finished = true;
      } finally {
        this.loading = false;
        this.loadingMore = false;

        // 记录最终状态
        writeLog(LogLevel.INFO, "index页面", "getFormListByPage请求完成", {
          finalState: {
            ordersCount: this.selfOrders.length,
            loading: false,
            loadingMore: false,
            finished: this.finished,
            pageNum: this.pageNum,
          },
        });
      }
    },

    // 获取巡视工单列表
    async fetchInspectOrders(isLoadMore = false) {
      // 添加DEBUG日志，跟踪调用来源
      console.log("fetchInspectOrders被调用", {
        isLoadMore,
        mainTab: this.mainTab,
        caller: new Error().stack,
      });

      // 记录日志
      writeLog(LogLevel.INFO, "index页面", "fetchInspectOrders被调用", {
        isLoadMore,
        mainTab: this.mainTab,
        hasInitialized: this.hasInitialized,
      });

      // 只在巡视tab下获取数据
      if (this.mainTab !== "inspect" || this.inspectSubTab !== "task") {
        this.inspectLoading = false;
        return;
      }

      if (!isLoadMore) {
        this.inspectLoading = true;
        this.inspectPageNum = 1;
        this.inspectFinished = false;
        this.inspectOrders = [];
      }

      try {
        // 修改这里:添加搜索参数
        const params = {
          // 如果有搜索值,添加到参数中
          ...(this.inspectSearchValue
            ? { towerName: this.inspectSearchValue }
            : {}),
        };

        // 如果是加载更多且已有数据，直接从缓存中分页
        if (isLoadMore && this.allInspectOrders.length > 0) {
          const startIndex = (this.inspectPageNum - 1) * this.inspectPageSize;
          const endIndex = startIndex + this.inspectPageSize;
          const newRows = this.allInspectOrders.slice(startIndex, endIndex);

          if (newRows.length > 0) {
            this.updateInspectFields(newRows);
            this.inspectOrders = [...this.inspectOrders, ...newRows];
          }

          this.inspectFinished =
            this.inspectOrders.length >= this.allInspectOrders.length;
          this.inspectLoading = false;
          this.inspectLoadingMore = false;
          return;
        }

        // 记录API调用前的参数信息
        writeLog(LogLevel.INFO, "index页面", "调用getFormListInformation前", {
          params,
          isLoadMore,
          currentTab: this.mainTab,
          searchValue: this.inspectSearchValue, // 添加搜索值记录
        });

        // 记录API开始时间
        const startTime = new Date().getTime();
        const response = await getFormListInformation(params); // 传入搜索参数
        // 计算API响应时间
        const apiTime = new Date().getTime() - startTime;

        // 记录完整的API响应
        writeLog(LogLevel.INFO, "index页面", "getFormListInformation返回结果", {
          resData: JSON.stringify(response),
          code: response?.code,
          msg: response?.msg,
          total: response?.total,
          rowsCount: response?.rows?.length,
          apiTime: `${apiTime}ms`,
          firstItem: response?.rows?.[0]
            ? JSON.stringify(response?.rows?.[0])
            : null,
        });

        // getFormListInformation返回的是直接的数组数据，不是分页格式
        let rows = [];
        let total = 0;

        if (response.data && Array.isArray(response.data)) {
          // 如果返回的是数组格式
          rows = response.data;
          total = rows.length;
        } else if (response.rows && Array.isArray(response.rows)) {
          // 如果返回的是分页格式
          rows = response.rows;
          total = response.total || rows.length;
        } else {
          // 兼容其他可能的格式
          rows = [];
          total = 0;
        }

        // 由于getFormListInformation可能不支持真正的分页，我们在前端模拟分页
        if (!isLoadMore) {
          // 首次加载，存储所有数据
          this.allInspectOrders = rows;
          // 显示第一页数据
          this.inspectOrders = rows.slice(0, this.inspectPageSize);
        } else {
          // 加载更多，从所有数据中取下一页
          const startIndex = (this.inspectPageNum - 1) * this.inspectPageSize;
          const endIndex = startIndex + this.inspectPageSize;
          const newRows = this.allInspectOrders.slice(startIndex, endIndex);
          this.inspectOrders = [...this.inspectOrders, ...newRows];
        }

        // 记录数据处理结果
        writeLog(LogLevel.INFO, "index页面", "处理getFormListInformation数据", {
          totalRecords: total,
          receivedRows: rows.length,
          currentDisplayCount: this.inspectOrders.length,
          allDataCount: this.allInspectOrders?.length || 0,
          isLoadingFinished: this.inspectOrders.length >= total,
        });

        // 更新字段显示
        if (this.inspectOrders.length > 0) {
          this.updateInspectFields(this.inspectOrders);
        }

        // 更新加载状态
        this.inspectFinished = this.inspectOrders.length >= total;
      } catch (error) {
        // 详细记录错误信息
        writeLog(
          LogLevel.ERROR,
          "index页面",
          "getFormListInformation调用失败",
          {
            error: error.message,
            stack: error.stack,
            params: {
              pageNum: this.inspectPageNum,
              pageSize: this.inspectPageSize,
              inspectSearchValue: this.inspectSearchValue,
            },
          }
        );
        console.error("获取巡视工单列表失败:", error);

        // 优先使用服务器返回的msg字段
        let errorMsg = "获取巡视工单列表失败";
        if (error.response?.msg) {
          errorMsg = error.response.msg;
        } else if (error.message) {
          errorMsg = error.message;
        }
        uni.showToast({
          title: errorMsg,
          icon: "none",
        });

        this.inspectFinished = true;
      } finally {
        this.inspectLoading = false;
        this.inspectLoadingMore = false;

        // 记录最终状态
        writeLog(LogLevel.INFO, "index页面", "getFormListInformation请求完成", {
          finalState: {
            ordersCount: this.inspectOrders.length,
            inspectLoading: false,
            inspectLoadingMore: false,
            inspectFinished: this.inspectFinished,
            inspectPageNum: this.inspectPageNum,
          },
        });
      }
    },

    async onLoadMore() {
      if (this.visitSubTab !== "task" || this.finished) return;
      this.loadingMore = true;
      this.pageNum++;
      await this.fetchOrders(true);
    },

    // 巡视加载更多
    async onInspectLoadMore() {
      if (this.inspectFinished) return;
      this.inspectLoadingMore = true;
      this.inspectPageNum++;

      // 如果有搜索条件，在前端分页
      if (this.inspectSearchValue && this.allInspectOrders.length > 0) {
        const filteredOrders = this.allInspectOrders.filter(
          (order) =>
            order.towerId && order.towerId.includes(this.inspectSearchValue)
        );
        const startIndex = (this.inspectPageNum - 1) * this.inspectPageSize;
        const endIndex = startIndex + this.inspectPageSize;
        const newRows = filteredOrders.slice(startIndex, endIndex);

        if (newRows.length > 0) {
          this.updateInspectFields(newRows);
          this.inspectOrders = [...this.inspectOrders, ...newRows];
        }

        this.inspectFinished =
          this.inspectOrders.length >= filteredOrders.length;
        this.inspectLoadingMore = false;
      } else {
        // 没有搜索条件，使用原有的加载更多逻辑
        await this.fetchInspectOrders(true);
      }
    },

    async onRefresh() {
      this.isRefreshing = true;
      try {
        writeLog(LogLevel.INFO, "index页面", "开始刷新数据", {
          tabType: this.activeTab,
        });
        // 重置分页相关数据
        this.pageNum = 1;
        this.finished = false;
        await this.fetchOrders();
        writeLog(LogLevel.INFO, "index页面", "刷新数据成功");
      } catch (error) {
        writeLog(LogLevel.ERROR, "index页面", "刷新数据失败", {
          error: error.message,
        });
      } finally {
        this.isRefreshing = false;
      }
    },

    onNavigationBarButtonTap() {
      const currentHistoryState = JSON.stringify(window.history.state);

      // 如果当前状态和初始状态相同，说明已经回到了初始页面
      if (currentHistoryState === this.initialHistoryState) {
        // 尝试调用安卓的返回方法
        if (window.clientApi && typeof window.clientApi.goBack === "function") {
          window.clientApi.goBack();
        } else if (
          window.Android &&
          typeof window.Android.goBack === "function"
        ) {
          window.Android.goBack();
        } else {
          console.log("找不到安卓返回方法");
        }
      } else {
        // 否则使用 Vue 路由返回
        this.$router.back();
      }
    },

    async onGetActionSheet(item, type) {
      if (type === "navigation") {
        writeLog(LogLevel.INFO, "index页面", "打开地图导航", {
          formId: item.formId,
          address: item.address,
        });
        this.actions = [
          {
            name: "地图导航",
            click: () => {
              if (isProd()) {
                // 生产环境使用思极地图微应用
                const url = encodeURI(
                  `zipapp://appid.${this.agentId}/index.html?scheme=route&dname=${item.address}&dlat=${item.latitude}&dlon=${item.longitude}&closeWithEndNavi=1`
                );
                wx.invoke(
                  "multiWindows_startWidget",
                  {
                    agentId: this.agentId,
                    window: {
                      windowId: "sgmap_app",
                      url: url,
                      openType: 1,
                      showAppBar: "false",
                    },
                  },
                  (res) => {
                    console.log("打开思极地图微应用 res = ", res);
                  }
                );
              } else {
                // 开发环境使用原有地图页面
                uni.navigateTo({
                  url: `/pages/map/index?address=${item.address}&cityName=${item.cityName}`,
                });
              }
            },
          },
        ];
        this.show = true;
      } else if (type === "phone") {
        writeLog(LogLevel.INFO, "index页面", "打开电话操作", {
          formId: item.formId,
          phone: item.customerPhone,
        });
        this.actions = [
          {
            name: item.customerPhone || "无电话信息",
            click: () => {
              if (item.customerPhone) {
                uni.makePhoneCall({
                  phoneNumber: item.customerPhone,
                });
              }
            },
          },
          {
            name: "已拨打电话，进行工单填报",
            click: async () => {
              await uni.navigateTo({
                url: `/pages/form/index?formId=${item.formId}&formDataId=${item.formDataId}`,
              });
            },
          },
        ];
        this.show = true;
      } else if (type === "exception") {
        writeLog(LogLevel.INFO, "index页面", "打开异常操作", {
          formId: item.formId,
        });
        this.actions = [
          {
            name: "无码",
            click: async () => {
              await uni.navigateTo({
                url: `/pages/form/index?formId=${item.formId}&formDataId=${item.formDataId}&codeExceptionType=1`,
              });
            },
          },
          {
            name: "缺码",
            click: async () => {
              await uni.navigateTo({
                url: `/pages/form/index?formId=${item.formId}&formDataId=${item.formDataId}&codeExceptionType=2`,
              });
            },
          },
          {
            name: "其他异常",
            click: async () => {
              await uni.navigateTo({
                url: `/pages/form/index?formId=${item.formId}&formDataId=${item.formDataId}&codeExceptionType=3`,
              });
            },
          },
        ];
        this.show = true;
      }
    },

    onSelect(action) {
      writeLog(LogLevel.INFO, "index页面", "选择操作项", {
        actionName: action.name,
      });
      if (action.click) {
        action.click();
      }
      this.show = false;
    },

    onCancel() {
      this.show = false;
    },

    goToScan() {
      writeLog(LogLevel.INFO, "index页面", "跳转到巡视扫码页面");
      uni.navigateTo({
        url: "/pages/qrcode/index?type=0",
      });
    },

    goToVisitScan() {
      writeLog(LogLevel.INFO, "index页面", "跳转到走访扫码页面");
      uni.navigateTo({
        url: "/pages/qrcode/index?type=1",
      });
    },

    goToBoard() {
      writeLog(LogLevel.INFO, "index页面", "跳转到看板页面");
      uni.navigateTo({
        url: "/pages/board/index",
      });
    },

    goToNoForm() {
      writeLog(LogLevel.INFO, "index页面", "跳转到无单填报页面");
      uni.navigateTo({
        url: "/pages/no-form/index",
      });
    },

    // 修改工单类型标签获取方法,与 index.js 保持一致
    getFormTypeLabel(typeNumber) {
      switch (typeNumber) {
        case "1":
          return "日常巡视";
        case "2":
          return "特殊巡视";
        case "3":
          return "日常走访";
        case "4":
          return "特殊走访";
        case "5":
          return "工单走访";
        case "6":
          return "工单巡视";
        case "7":
          return "默认走访";
        case "8":
          return "默认巡视";
        default:
          return "未知类型";
      }
    },

    // 获取工单来源标签
    getDatasource(source) {
      const sources = {
        207107: "207107-服务申请接单下发",
        "0207132": "0207132-服务申请受理下发",
        "0207077": "0207077-业务上收-非故障类业务省接单下发",
        "0207131": "0207131-非故障受理下发",
        "0207025": "0207025-故障受理下发",
      };
      return sources[source] || "207107-服务申请接单下发";
    },

    // 添加新方法
    getStatusText(status) {
      const statusMap = {
        0: "超时未完成",
        1: "已创建",
        2: "进行中",
        3: "按时完成",
        4: "超时完成",
      };
      // 确保转换为数字
      const statusNum = Number(status);
      return statusMap[statusNum] || `未知状态(${status})`;
    },

    // 根据工单类型更新显示字段
    updateFields(orders) {
      if (!Array.isArray(orders) || orders.length === 0) {
        writeLog(LogLevel.WARN, "index页面", "updateFields: 无有效数据", {
          orders,
        });
        return;
      }

      orders.forEach((order, index) => {
        if (!order) {
          writeLog(
            LogLevel.WARN,
            "index页面",
            `updateFields: 第${index}条数据无效`
          );
          return;
        }

        let fields = [];

        // 走访任务列表固定显示四个字段：计划工单编号、客户名称、客户编号、截止日期
        fields.push({
          label: "计划工单编号:",
          key: "formDataId",
          value: order.formDataId || "暂无",
        });

        fields.push({
          label: "客户名称:",
          key: "customName",
          value: order.customName || "暂无",
        });

        fields.push({
          label: "客户编号:",
          key: "customId",
          value: order.customId || "暂无",
        });

        fields.push({
          label: "截止日期:",
          key: "endTime",
          value: order.endTime || "暂无",
        });

        // 记录字段更新
        writeLog(LogLevel.INFO, "index页面", `更新工单${index + 1}字段`, {
          fieldsCount: fields.length,
        });
        order.fields = fields;
      });
    },

    // 更新巡视工单字段显示
    updateInspectFields(orders) {
      if (!Array.isArray(orders) || orders.length === 0) {
        writeLog(
          LogLevel.WARN,
          "index页面",
          "updateInspectFields: 无有效数据",
          {
            orders,
          }
        );
        return;
      }

      orders.forEach((order, index) => {
        if (!order) {
          writeLog(
            LogLevel.WARN,
            "index页面",
            `updateInspectFields: 第${index}条数据无效`
          );
          return;
        }

        let fields = [];

        // 巡视任务列表固定显示六个字段：工单编号、台区编号、台区名称、计划编号、状态、创建时间
        fields.push({
          label: "工单编号:",
          key: "formDataId",
          value: order.formDataId || "暂无",
        });

        fields.push({
          label: "台区编号:",
          key: "towerId",
          value: order.towerId || "暂无",
        });

        fields.push({
          label: "台区名称:",
          key: "towerName",
          value: order.towerName || "暂无",
        });

        fields.push({
          label: "计划编号:",
          key: "planId",
          value: order.planId || "暂无",
        });

        fields.push({
          label: "状态:",
          key: "formStatus",
          value: order.formStatus, // 这里直接传递原始值，让模板来处理显示
        });

        fields.push({
          label: "创建时间:",
          key: "createTime",
          value: order.createTime || "暂无",
        });

        // 记录字段更新
        writeLog(LogLevel.INFO, "index页面", `更新巡视工单${index + 1}字段`, {
          fieldsCount: fields.length,
          formDataId: order.formDataId,
        });
        order.fields = fields;
      });
    },

    // 添加获取工单编号标签的方法
    getFormTypeLabelByType(typeNumber) {
      // 只需要区分是否是申请编号
      if (["5", "6"].includes(typeNumber)) {
        return "申请编号:";
      }
      return "计划工单编号:"; // 所有其他类型都显示"计划工单编号"
    },

    // 修改按钮显示逻辑
    shouldShowButton(formType, buttonType, tabType) {
      if (!formType || !buttonType) {
        writeLog(LogLevel.WARN, "index页面", "shouldShowButton: 参数无效", {
          formType,
          buttonType,
          tabType,
        });
        return false;
      }

      // 针对当前实际使用的Tab结构调整逻辑
      // 走访任务列表应该当作原有的工单任务逻辑处理
      const isTaskTab = this.visitSubTab === "task";
      const isInspectTab = tabType === "inspect";

      if (!isTaskTab && !isInspectTab) {
        // 自主填报不显示导航按钮
        if (buttonType === "navigation") return false;
        // 只有当有电话号码时才显示电话按钮
        if (buttonType === "phone") {
          const currentOrder = this.currentOrders.find(
            (order) => order.formType === formType
          );
          return currentOrder?.customerPhone;
        }
        // 在自主填报中显示填报按钮
        if (buttonType === "submit") return true;
        return true; // 异常按钮始终显示
      }

      // 巡视tab的按钮逻辑
      if (isInspectTab) {
        if (buttonType === "navigation") {
          return !["1", "2", "6"].includes(formType);
        }
        if (buttonType === "phone") {
          return !["1", "2", "4", "6", "8"].includes(formType);
        }
        // 巡视中始终显示填报按钮
        if (buttonType === "submit") return true;
        // 巡视中不显示异常按钮
        if (buttonType === "exception") return false;
        return true;
      }

      // 原有的工单任务逻辑
      if (buttonType === "navigation") {
        return !["1", "2", "6"].includes(formType);
      }
      if (buttonType === "phone") {
        return !["1", "2", "4", "6", "8"].includes(formType);
      }
      // 工单任务中不显示填报按钮
      if (buttonType === "submit") return false;
      return true; // 异常按钮始终显示
    },

    // 获取当前工单类型下显示的按钮数量
    getVisibleButtonCount(formType, tabType) {
      let count = 0;
      if (this.shouldShowButton(formType, "navigation", tabType)) count++;
      if (this.shouldShowButton(formType, "phone", tabType)) count++;
      if (this.shouldShowButton(formType, "submit", tabType)) count++;
      if (this.shouldShowButton(formType, "exception", tabType)) count++;
      return count;
    },

    // 获取按钮宽度类
    getButtonWidthClass(formType, tabType) {
      const count = this.getVisibleButtonCount(formType, tabType);
      return `w-[${Math.floor(100 / count)}%]`;
    },

    onSubmitForm(item) {
      if (!item?.formId || !item?.formDataId) {
        writeLog(LogLevel.ERROR, "index页面", "工单数据无效，无法填报", {
          item,
        });
        uni.showToast({
          title: "工单数据无效",
          icon: "none",
        });
        return;
      }

      writeLog(LogLevel.INFO, "index页面", "跳转到表单填报页面", {
        formId: item.formId,
        formDataId: item.formDataId,
      });

      uni.navigateTo({
        url: `/pages/form/index?formId=${item.formId}&formDataId=${item.formDataId}`,
        fail: (error) => {
          writeLog(LogLevel.ERROR, "index页面", "跳转失败", { error });
          uni.showToast({
            title: "页面跳转失败",
            icon: "none",
          });
        },
      });
    },

    // 巡视工单填报
    onSubmitInspectForm(item) {
      if (!item?.formId || !item?.formDataId) {
        writeLog(LogLevel.ERROR, "index页面", "巡视工单数据无效，无法填报", {
          item,
        });
        uni.showToast({
          title: "工单数据无效",
          icon: "none",
        });
        return;
      }

      writeLog(LogLevel.INFO, "index页面", "跳转到巡视表单填报页面", {
        formId: item.formId,
        formDataId: item.formDataId,
        towerId: item.towerId,
      });

      uni.navigateTo({
        url: `/pages/form/index?formId=${item.formId}&formDataId=${item.formDataId}`,
        fail: (error) => {
          writeLog(LogLevel.ERROR, "index页面", "跳转失败", { error });
          uni.showToast({
            title: "页面跳转失败",
            icon: "none",
          });
        },
      });
    },

    // 巡视列表填报 - 直接跳转到表单填写页面
    onInspectFormSubmit(item) {
      if (!item?.formId || !item?.formDataId) {
        writeLog(LogLevel.ERROR, "index页面", "巡视工单数据无效，无法填报", {
          item,
        });
        uni.showToast({
          title: "工单数据无效",
          icon: "none",
        });
        return;
      }

      writeLog(LogLevel.INFO, "index页面", "巡视列表填报，直接跳转到表单页面", {
        formId: item.formId,
        formDataId: item.formDataId,
        towerId: item.towerId,
        planId: item.planId,
      });

      // 直接跳转到表单填写页面
      uni.navigateTo({
        url: `/pages/form/index?formId=${item.formId}&formDataId=${item.formDataId}`,
        fail: (error) => {
          writeLog(LogLevel.ERROR, "index页面", "跳转到表单页面失败", {
            error,
          });
          uni.showToast({
            title: "页面跳转失败",
            icon: "none",
          });
        },
      });
    },
    formDataTimeFormat(time) {
      //转换成年月日时分秒
      if (!time) {
        return "暂无";
      }

      let date = new Date(time);

      // 检查日期是否有效
      if (isNaN(date.getTime())) {
        return "暂无";
      }

      let Y = date.getFullYear() + "-";
      let M =
        (date.getMonth() + 1 < 10
          ? "0" + (date.getMonth() + 1)
          : date.getMonth() + 1) + "-";
      let D =
        (date.getDate() < 10 ? "0" + date.getDate() : date.getDate()) + " ";
      let h =
        (date.getHours() < 10 ? "0" + date.getHours() : date.getHours()) + ":";
      let m =
        (date.getMinutes() < 10 ? "0" + date.getMinutes() : date.getMinutes()) +
        ":";
      let s =
        date.getSeconds() < 10 ? "0" + date.getSeconds() : date.getSeconds();
      return Y + M + D + h + m + s;
    },
    onSearch() {
      this.pageNum = 1;
      this.finished = false;
      this.fetchOrders();
    },

    // 巡视搜索
    onInspectSearch() {
      this.inspectPageNum = 1;
      this.inspectFinished = false;
      // 直接调用 fetchInspectOrders 获取新数据,不再在前端过滤
      this.fetchInspectOrders();
    },

    // 巡视刷新
    async onInspectRefresh() {
      this.inspectPageNum = 1;
      this.inspectFinished = false;
      await this.fetchInspectOrders();
    },

    async goToManualForm() {
      try {
        writeLog(LogLevel.INFO, "index页面", "跳转到走访手动填报页面");

        // 直接跳转到no-form页面，传递type=1参数
        uni.navigateTo({
          url: `/pages/no-form/index?type=1`,
        });
      } catch (error) {
        console.error("跳转到走访手动填报页面失败:", error);
        uni.showToast({
          title: "页面跳转失败",
          icon: "error",
        });
      }
    },

    // 修改巡视手动填报方法
    async goToInspectManualForm() {
      try {
        writeLog(LogLevel.INFO, "index页面", "跳转到巡视手动填报页面");

        // 直接跳转到no-form页面，传递type=2参数
        uni.navigateTo({
          url: `/pages/no-form/index?type=2`,
        });
      } catch (error) {
        console.error("跳转到巡视手动填报页面失败:", error);
        uni.showToast({
          title: "页面跳转失败",
          icon: "error",
        });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.index-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}
.text-type {
  color: #00706b;
}
.fixed-scan-btn {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 120px;
  height: 44px;
  background-color: #1989fa;
  color: white;
  border-radius: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  z-index: 100;
}
.scan-btn-inner {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 骨架屏样式 */
.skeleton-card {
  background: #fff;
  border-radius: 8px;
  padding: 16px;
  margin: 0 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.skeleton-header {
  height: 20px;
  background: #f2f3f5;
  border-radius: 4px;
  margin-bottom: 12px;
  width: 30%;
  margin-left: auto;
}

.skeleton-content {
  margin-bottom: 16px;
}

.skeleton-row {
  height: 16px;
  background: #f2f3f5;
  border-radius: 4px;
  margin-bottom: 8px;
}

.skeleton-row:nth-child(1) {
  width: 60%;
}

.skeleton-row:nth-child(2) {
  width: 80%;
}

.skeleton-row:nth-child(3) {
  width: 70%;
}

.skeleton-row:nth-child(4) {
  width: 50%;
}

.skeleton-footer {
  display: flex;
  gap: 8px;
}

.skeleton-button {
  height: 32px;
  background: #f2f3f5;
  border-radius: 4px;
  flex: 1;
}
</style>
